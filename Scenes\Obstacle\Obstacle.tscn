[gd_scene load_steps=4 format=2]

[ext_resource path="res://Scenes/Obstacle/Obstacle.gd" type="Script" id=1]
[ext_resource path="res://Art/Environment/full-pipe.png" type="Texture" id=2]

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 10, 96.2285 )

[node name="Obstacle" type="Node2D"]
position = Vector2( 240, 320 )
script = ExtResource( 1 )

[node name="TopPipe" type="Area2D" parent="."]
position = Vector2( 0, -400 )
collision_layer = 2
collision_mask = 8
__meta__ = {
"_edit_group_": true
}

[node name="Sprite" type="Sprite" parent="TopPipe"]
rotation = 3.14159
texture = ExtResource( 2 )

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="TopPipe"]
position = Vector2( 0.253062, 15.0854 )
scale = Vector2( 1.86467, 0.965176 )
polygon = PoolVector2Array( -28.5145, -326.96, 27.9355, -326.96, 28.1502, 296.919, -28.0852, 296.704 )

[node name="BottomPipe" type="Area2D" parent="."]
position = Vector2( 0, 400 )
collision_layer = 2
collision_mask = 8
__meta__ = {
"_edit_group_": true
}

[node name="Sprite" type="Sprite" parent="BottomPipe"]
texture = ExtResource( 2 )

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="BottomPipe"]
position = Vector2( 1.2864, -15.8772 )
scale = Vector2( 1.9004, 0.97224 )
polygon = PoolVector2Array( 27.937, 326.616, -28.4563, 326.616, -28.1927, -296.868, 28.2006, -296.868 )

[node name="ScoreArea" type="Area2D" parent="."]
collision_layer = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="ScoreArea"]
shape = SubResource( 1 )
[connection signal="area_entered" from="TopPipe" to="." method="_on_TopPipe_area_entered"]
[connection signal="area_entered" from="ScoreArea" to="." method="_on_ScoreArea_area_entered"]
