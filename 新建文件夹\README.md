# Flappy 9/11 - Web版游戏

这是一个基于原始Godot项目重新构建的完整Web版Flappy 9/11游戏。

## 🎮 游戏特性

### 核心功能
- ✅ **完整的游戏逻辑** - 基于Godot源代码重写
- ✅ **物理系统** - 重力、跳跃、旋转等物理效果
- ✅ **碰撞检测** - 精确的管道和地面碰撞
- ✅ **计分系统** - 实时计分和最高分保存
- ✅ **游戏状态管理** - 启动画面、游戏中、死亡、计分板
- ✅ **响应式设计** - 支持桌面和移动设备

### 控制方式
- 🖱️ **鼠标左键** - 让飞机飞起来
- ⌨️ **空格键** - 让飞机飞起来  
- 📱 **触摸屏幕** - 移动设备上点击屏幕

### 游戏机制
- **重力系统** - 飞机会自然下落
- **跳跃机制** - 点击/按键让飞机向上飞
- **旋转动画** - 飞机会根据速度旋转
- **管道生成** - 随机高度的管道障碍
- **得分机制** - 通过管道获得分数
- **最高分保存** - 自动保存到本地存储

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）
1. 双击 `start_game.bat`
2. 选择服务器类型（推荐Python）
3. 在浏览器中访问显示的地址

### 方法2：手动启动服务器

#### Python（推荐）
```bash
python -m http.server 8000
```
然后访问 `http://localhost:8000`

#### Node.js
```bash
npx http-server -p 8080
```
然后访问 `http://localhost:8080`

#### PHP
```bash
php -S localhost:8000
```
然后访问 `http://localhost:8000`

## 📁 项目结构

```
新建文件夹/
├── index.html              # 主游戏页面
├── start_game.bat          # 启动脚本
├── README.md               # 项目说明
├── css/
│   ├── reset.css           # CSS重置
│   └── main.css            # 主样式文件
├── js/
│   ├── game.js             # 主游戏逻辑（新重写）
│   ├── jquery.transit.min.js  # jQuery动画库
│   └── buzz.min.js         # 音频库
└── assets/
    ├── sounds/             # 音频文件夹
    │   └── README.md       # 音频文件说明
    ├── *.png               # 游戏图片资源
    └── font_*.png          # 字体图片
```

## 🔧 技术实现

### 基于Godot源代码的改进
- **物理常数** - 直接移植Godot中的重力、速度等参数
- **游戏状态** - 实现了AUTO_PILOT、PLAYING、CRASHING等状态
- **碰撞检测** - 精确的矩形碰撞检测
- **管道生成** - 基于Godot的ObstacleSpawner逻辑
- **计分机制** - 完全按照原版实现

### 技术栈
- **HTML5** - 游戏容器和结构
- **CSS3** - 样式和动画
- **JavaScript (ES6)** - 游戏逻辑
- **jQuery** - DOM操作和事件处理
- **Buzz.js** - 音频处理（可选）

### 性能优化
- **requestAnimationFrame** - 流畅的游戏循环
- **CSS动画** - 硬件加速的背景动画
- **事件委托** - 高效的事件处理
- **本地存储** - 最高分持久化

## 🎵 音频设置

游戏支持音频，但音频文件需要单独添加：

1. 查看 `assets/sounds/README.md` 了解需要的音频文件
2. 从原始Godot项目复制音频文件
3. 或使用免费音效库获取音频
4. 支持 OGG 和 MP3 格式

**注意**：即使没有音频文件，游戏也能正常运行。

## 🐛 调试模式

在URL后添加 `?debug` 启用调试模式：
```
http://localhost:8000?debug
```

调试模式会显示：
- 碰撞边界框
- 控制台日志
- 游戏状态信息

## 📱 移动端支持

- **触摸控制** - 支持触摸屏操作
- **响应式布局** - 自动适配屏幕尺寸
- **防止缩放** - 禁用双击缩放
- **全屏支持** - 移动端友好的全屏体验

## 🔄 与原版的差异

### 改进之处
- ✅ 更好的Web兼容性
- ✅ 响应式设计
- ✅ 错误处理和容错机制
- ✅ 调试模式
- ✅ 移动端优化

### 保持一致
- ✅ 相同的物理参数
- ✅ 相同的游戏机制
- ✅ 相同的视觉效果
- ✅ 相同的计分系统

## 🚀 部署到Web

### GitHub Pages
1. 将文件夹内容推送到GitHub仓库
2. 启用GitHub Pages
3. 访问生成的URL

### Netlify
1. 将文件夹拖拽到Netlify
2. 获得自动生成的URL

### 自有服务器
1. 上传文件到服务器
2. 确保支持静态文件服务
3. 配置正确的MIME类型

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

基于原始Godot项目，遵循相同的许可证条款。
