; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=4

_global_script_classes=[  ]
_global_script_class_icons={

}

[application]

config/name="Flappy 9/11"
run/main_scene="res://Scenes/Main/Main.tscn"
config/icon="res://icon.png"

[display]

window/size/width=480
window/size/height=800
window/dpi/allow_hidpi=true
window/handheld/orientation="portrait"
window/stretch/mode="2d"
window/stretch/aspect="keep"

[input]

fly={
"deadzone": 0.5,
"events": [ Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"alt":false,"shift":false,"control":false,"meta":false,"command":false,"pressed":false,"scancode":32,"unicode":0,"echo":false,"script":null)
, Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"alt":false,"shift":false,"control":false,"meta":false,"command":false,"button_mask":0,"position":Vector2( 0, 0 ),"global_position":Vector2( 0, 0 ),"factor":1.0,"button_index":1,"pressed":false,"doubleclick":false,"script":null)
 ]
}

[layer_names]

2d_physics/layer_1="Bird"
2d_physics/layer_2="Pipes"
2d_physics/layer_3="ScoreZone"
2d_physics/layer_4="DestructionZone"
2d_physics/layer_5="Ground"

[rendering]

environment/default_environment="res://default_env.tres"
quality/driver/driver_name="GLES2"
vram_compression/import_etc=true
vram_compression/import_etc2=false

[html5]

export/icon=true
