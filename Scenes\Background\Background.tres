[gd_resource type="Animation" format=2]

[resource]

resource_name = "scrolling"
length = 30.0
loop = true
step = 0.1
tracks/0/type = "value"
tracks/0/path = NodePath(".:region_rect")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 30 ),
"transitions": <PERSON>RealArray( 1, 1 ),
"update": 0,
"values": [ Rect2( 0, 0, 1408, 1536 ), Rect2( 3072, 0, 1408, 1536 ) ]
}

