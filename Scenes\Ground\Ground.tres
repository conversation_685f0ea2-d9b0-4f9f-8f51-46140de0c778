[gd_resource type="Animation" format=2]

[resource]

resource_name = "scrolling"
length = 1.7
loop = true
step = 0.1
tracks/0/type = "value"
tracks/0/path = NodePath("Sprite:region_rect")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Rect2( 0, 1328, 2050, 194 ), Rect2( 1025, 1328, 2050, 194 ) ]
}

