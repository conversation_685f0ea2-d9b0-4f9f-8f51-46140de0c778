/**
 * Flappy 9/11 Web Game
 * Based on Godot source code logic
 */

// Game constants (from Godot Bird.gd)
const GAME_CONFIG = {
    FLIGHT_CEILING: 28,
    GRAVITY: 0.25,  // Adjusted for web (original was 8 per frame)
    FLY_UPWARD_VELOCITY: -4.6,  // Adjusted for web (original was -225)
    MAX_DOWNWARD_VELOCITY: 8,   // Adjusted for web (original was 300)
    ROTATION_VELOCITY: 250,
    FLYING_ROTATION: -45,
    DIVING_ROTATION: 90,
    PIPE_GAP_SIZE: 220,
    PIPE_WIDTH: 52,
    PIPE_SPEED: 150,  // pixels per second
    GROUND_HEIGHT: 100
};

// Game states (from Godot)
const GAME_STATES = {
    AUTO_PILOT: 0,
    PLAYING: 1,
    CRASHING: 2,
    CRASHED: 3,
    SCORE_SCREEN: 4
};

// Game variables
let gameState = GAME_STATES.AUTO_PILOT;
let player = {
    x: 60,
    y: 200,
    velocity: 0,
    rotation: 0,
    shouldRotate: false
};

let game = {
    score: 0,
    highScore: 0,
    pipes: [],
    lastTime: 0,
    pipeTimer: 0,
    pipeInterval: 1400, // milliseconds
    gameLoop: null,
    screenWidth: 500,
    screenHeight: 420
};

// Audio system
let audioEnabled = true;
let sounds = {};

// Initialize game
function initGame() {
    console.log('Initializing Flappy 9/11 Web Game...');

    try {
        // Show loading indicator
        showLoading();

        // Load high score from localStorage
        const savedHighScore = localStorage.getItem('flappy911_highscore');
        if (savedHighScore) {
            game.highScore = parseInt(savedHighScore);
        }

        // Initialize audio (with fallback for missing files)
        initAudio();

        // Set up event listeners
        setupEventListeners();

        // Hide loading indicator
        hideLoading();

        // Start in splash screen mode
        showSplashScreen();

        console.log('Game initialized successfully');
    } catch (error) {
        console.error('Failed to initialize game:', error);
        showError('Failed to initialize game: ' + error.message);
    }
}

// Show loading indicator
function showLoading() {
    $('#loading').show();
}

// Hide loading indicator
function hideLoading() {
    $('#loading').hide();
}

// Show error message
function showError(message) {
    $('#error-message').text(message);
    $('#error').show();
    setTimeout(() => {
        $('#error').fadeOut();
    }, 5000);
}

// Audio initialization with error handling
function initAudio() {
    try {
        // Only initialize audio if buzz.js is available
        if (typeof buzz !== 'undefined') {
            sounds = {
                jump: new buzz.sound("assets/sounds/sfx_wing", { formats: ["wav", "ogg", "mp3"] }),
                score: new buzz.sound("assets/sounds/sfx_point", { formats: ["wav", "ogg", "mp3"] }),
                hit: new buzz.sound("assets/sounds/sfx_hit", { formats: ["wav", "ogg", "mp3"] }),
                die: new buzz.sound("assets/sounds/sfx_die", { formats: ["wav", "ogg", "mp3"] }),
                swoosh: new buzz.sound("assets/sounds/sfx_swooshing", { formats: ["wav", "ogg", "mp3"] })
            };
            
            // Set volume
            Object.values(sounds).forEach(sound => {
                sound.setVolume(30);
            });
        }
    } catch (error) {
        console.warn('Audio initialization failed:', error);
        audioEnabled = false;
    }
}

// Play sound with error handling
function playSound(soundName) {
    if (audioEnabled && sounds[soundName]) {
        try {
            sounds[soundName].stop();
            sounds[soundName].play();
        } catch (error) {
            console.warn(`Failed to play sound: ${soundName}`, error);
        }
    }
}

// Event listeners
function setupEventListeners() {
    // Keyboard input
    $(document).keydown(function(e) {
        if (e.keyCode === 32) { // Spacebar
            e.preventDefault();
            handleInput();
        }
    });
    
    // Mouse/touch input
    if ('ontouchstart' in window) {
        $(document).on('touchstart', function(e) {
            e.preventDefault();
            handleInput();
        });
    } else {
        $(document).on('mousedown', function(e) {
            e.preventDefault();
            handleInput();
        });
    }
    
    // Replay button
    $('#replay').click(function() {
        if (gameState === GAME_STATES.SCORE_SCREEN) {
            restartGame();
        }
    });
}

// Handle input based on game state
function handleInput() {
    switch (gameState) {
        case GAME_STATES.AUTO_PILOT:
            startGame();
            break;
        case GAME_STATES.PLAYING:
            playerJump();
            break;
        case GAME_STATES.SCORE_SCREEN:
            // Allow restart via input
            restartGame();
            break;
    }
}

// Show splash screen
function showSplashScreen() {
    gameState = GAME_STATES.AUTO_PILOT;
    
    // Reset player
    player.x = 60;
    player.y = 200;
    player.velocity = 0;
    player.rotation = 0;
    player.shouldRotate = false;
    
    // Reset game
    game.score = 0;
    game.pipes = [];
    
    // Update UI
    updatePlayerPosition();
    setBigScore();
    
    // Clear pipes
    $('.pipe').remove();
    
    // Show splash, hide scoreboard
    $('#splash').stop().animate({opacity: 1}, 500);
    $('#scoreboard').hide();
    
    // Start animations
    $('.animated').css('animation-play-state', 'running');
    
    playSound('swoosh');
}

// Start the game
function startGame() {
    console.log('Starting game...');
    gameState = GAME_STATES.PLAYING;
    
    // Hide splash
    $('#splash').stop().animate({opacity: 0}, 500);
    
    // Reset timers
    game.lastTime = performance.now();
    game.pipeTimer = 0;
    
    // Start game loop
    if (game.gameLoop) {
        cancelAnimationFrame(game.gameLoop);
    }
    gameLoop();
    
    // First jump
    playerJump();
    
    playSound('swoosh');
}

// Main game loop
function gameLoop() {
    if (gameState !== GAME_STATES.PLAYING && gameState !== GAME_STATES.CRASHING) {
        return;
    }
    
    const currentTime = performance.now();
    const deltaTime = (currentTime - game.lastTime) / 1000; // Convert to seconds
    game.lastTime = currentTime;
    
    // Update physics
    updatePhysics(deltaTime);
    
    // Update pipes
    updatePipes(deltaTime);
    
    // Check collisions
    checkCollisions();
    
    // Update visuals
    updatePlayerPosition();
    
    // Continue loop
    game.gameLoop = requestAnimationFrame(gameLoop);
}

// Update physics (based on Godot Bird.gd)
function updatePhysics(deltaTime) {
    if (gameState === GAME_STATES.PLAYING) {
        // Apply gravity
        player.velocity = Math.min(player.velocity + GAME_CONFIG.GRAVITY, GAME_CONFIG.MAX_DOWNWARD_VELOCITY);

        // Update position
        player.y += player.velocity * 60 * deltaTime; // Scale for 60fps equivalent

        // Keep player on screen
        player.y = Math.max(player.y, GAME_CONFIG.FLIGHT_CEILING);

        // Update rotation
        if (player.shouldRotate) {
            const targetRotation = Math.min(player.velocity / GAME_CONFIG.MAX_DOWNWARD_VELOCITY * GAME_CONFIG.DIVING_ROTATION, GAME_CONFIG.DIVING_ROTATION);
            player.rotation = Math.min(player.rotation + GAME_CONFIG.ROTATION_VELOCITY * deltaTime, targetRotation);
        }
    } else if (gameState === GAME_STATES.CRASHING) {
        // Maximum gravity when crashing
        player.velocity = GAME_CONFIG.MAX_DOWNWARD_VELOCITY;
        player.y += player.velocity * 60 * deltaTime;

        // Nosedive rotation
        player.rotation = Math.min(player.rotation + GAME_CONFIG.ROTATION_VELOCITY * deltaTime, GAME_CONFIG.DIVING_ROTATION);
    }
}

// Player jump
function playerJump() {
    if (gameState === GAME_STATES.PLAYING) {
        player.velocity = GAME_CONFIG.FLY_UPWARD_VELOCITY;
        player.rotation = GAME_CONFIG.FLYING_ROTATION;
        player.shouldRotate = false;

        // Start rotation timer (like Godot's RotationBeginTimeout)
        setTimeout(() => {
            player.shouldRotate = true;
        }, 750);

        playSound('jump');
    }
}

// Update player visual position
function updatePlayerPosition() {
    const $player = $('#player');
    $player.css({
        top: player.y + 'px',
        left: player.x + 'px',
        transform: `rotate(${player.rotation}deg)`
    });
}

// Update pipes
function updatePipes(deltaTime) {
    // Move existing pipes
    $('.pipe').each(function() {
        const $pipe = $(this);
        let currentLeft = parseInt($pipe.css('left')) || 0;
        currentLeft -= GAME_CONFIG.PIPE_SPEED * deltaTime;
        $pipe.css('left', currentLeft + 'px');

        // Remove pipes that are off screen
        if (currentLeft < -GAME_CONFIG.PIPE_WIDTH) {
            $pipe.remove();
        }
    });

    // Generate new pipes
    game.pipeTimer += deltaTime * 1000; // Convert to milliseconds
    if (game.pipeTimer >= game.pipeInterval) {
        generatePipe();
        game.pipeTimer = 0;
    }
}

// Generate a new pipe (based on Godot ObstacleSpawner.gd)
function generatePipe() {
    const pipeGap = GAME_CONFIG.PIPE_GAP_SIZE;
    const maxHeight = game.screenHeight - pipeGap - GAME_CONFIG.GROUND_HEIGHT;
    const minHeight = 50;

    // Random pipe height
    const upperPipeHeight = Math.floor(Math.random() * (maxHeight - minHeight) + minHeight);
    const lowerPipeHeight = game.screenHeight - upperPipeHeight - pipeGap - GAME_CONFIG.GROUND_HEIGHT;

    // Create pipe HTML
    const pipeHtml = `
        <div class="pipe animated" style="left: ${game.screenWidth}px;">
            <div class="pipe_upper" style="height: ${upperPipeHeight}px;"></div>
            <div class="pipe_lower" style="height: ${lowerPipeHeight}px;"></div>
            <div class="pipe_score_zone" style="top: ${upperPipeHeight}px; height: ${pipeGap}px;"></div>
        </div>
    `;

    $('#flyarea').append(pipeHtml);
}

// Check collisions
function checkCollisions() {
    if (gameState !== GAME_STATES.PLAYING) return;

    const playerRect = {
        left: player.x,
        top: player.y,
        right: player.x + 34, // Approximate player width
        bottom: player.y + 24  // Approximate player height
    };

    // Check ground collision
    if (playerRect.bottom >= game.screenHeight - GAME_CONFIG.GROUND_HEIGHT) {
        playerDeath();
        return;
    }

    // Check ceiling collision
    if (playerRect.top <= GAME_CONFIG.FLIGHT_CEILING) {
        player.y = GAME_CONFIG.FLIGHT_CEILING;
        player.velocity = 0;
    }

    // Check pipe collisions
    $('.pipe').each(function() {
        const $pipe = $(this);
        const pipeLeft = parseInt($pipe.css('left'));
        const pipeRight = pipeLeft + GAME_CONFIG.PIPE_WIDTH;

        // Check if player is in pipe's horizontal range
        if (playerRect.right > pipeLeft && playerRect.left < pipeRight) {
            const $upperPipe = $pipe.find('.pipe_upper');
            const $lowerPipe = $pipe.find('.pipe_lower');
            const $scoreZone = $pipe.find('.pipe_score_zone');

            const upperPipeHeight = parseInt($upperPipe.css('height'));
            const lowerPipeTop = game.screenHeight - parseInt($lowerPipe.css('height')) - GAME_CONFIG.GROUND_HEIGHT;

            // Check collision with pipes
            if (playerRect.top < upperPipeHeight || playerRect.bottom > lowerPipeTop) {
                playerDeath();
                return false;
            }

            // Check scoring
            if (!$scoreZone.hasClass('scored') && playerRect.left > pipeRight) {
                $scoreZone.addClass('scored');
                playerScore();
            }
        }
    });
}

// Player scores a point
function playerScore() {
    game.score++;
    setBigScore();
    playSound('score');
    console.log('Score:', game.score);
}

// Player death
function playerDeath() {
    console.log('Player died');
    gameState = GAME_STATES.CRASHING;

    // Stop animations
    $('.animated').css('animation-play-state', 'paused');

    // Play death sound
    playSound('hit');

    // Stop game loop
    if (game.gameLoop) {
        cancelAnimationFrame(game.gameLoop);
        game.gameLoop = null;
    }

    // Animate player falling
    setTimeout(() => {
        gameState = GAME_STATES.CRASHED;
        showScoreScreen();
    }, 1000);
}

// Show score screen
function showScoreScreen() {
    gameState = GAME_STATES.SCORE_SCREEN;

    // Update high score
    if (game.score > game.highScore) {
        game.highScore = game.score;
        localStorage.setItem('flappy911_highscore', game.highScore.toString());
    }

    // Update score display
    setSmallScore();
    setHighScore();

    // Show scoreboard
    $('#scoreboard').show().css({opacity: 0, transform: 'translateY(40px)'});
    $('#replay').css({opacity: 0, transform: 'translateY(40px)'});

    $('#scoreboard').animate({
        opacity: 1,
        transform: 'translateY(0px)'
    }, 600, function() {
        $('#replay').animate({
            opacity: 1,
            transform: 'translateY(0px)'
        }, 600);
    });

    playSound('swoosh');
}

// Restart game
function restartGame() {
    console.log('Restarting game');

    // Hide scoreboard
    $('#scoreboard').animate({
        opacity: 0,
        transform: 'translateY(-40px)'
    }, 500, function() {
        $('#scoreboard').hide();
        showSplashScreen();
    });

    playSound('swoosh');
}

// Set big score display
function setBigScore() {
    const $bigscore = $('#bigscore');
    $bigscore.empty();

    if (gameState === GAME_STATES.PLAYING) {
        const scoreStr = game.score.toString();
        for (let i = 0; i < scoreStr.length; i++) {
            $bigscore.append(`<img src="assets/font_big_${scoreStr[i]}.png" alt="${scoreStr[i]}">`);
        }
    }
}

// Set small score display
function setSmallScore() {
    const $currentscore = $('#currentscore');
    $currentscore.empty();

    const scoreStr = game.score.toString();
    for (let i = 0; i < scoreStr.length; i++) {
        $currentscore.append(`<img src="assets/font_small_${scoreStr[i]}.png" alt="${scoreStr[i]}">`);
    }
}

// Set high score display
function setHighScore() {
    const $highscore = $('#highscore');
    $highscore.empty();

    const scoreStr = game.highScore.toString();
    for (let i = 0; i < scoreStr.length; i++) {
        $highscore.append(`<img src="assets/font_small_${scoreStr[i]}.png" alt="${scoreStr[i]}">`);
    }
}

// Initialize when document is ready
$(document).ready(function() {
    console.log('Document ready, initializing game...');

    // Check for debug mode
    if (window.location.search.includes('debug')) {
        $('.boundingbox').show();
        console.log('Debug mode enabled');
    }

    // Initialize game
    initGame();
});

// Export for debugging
window.FlappyGame = {
    player,
    game,
    gameState,
    GAME_STATES,
    restartGame,
    playerJump
};
