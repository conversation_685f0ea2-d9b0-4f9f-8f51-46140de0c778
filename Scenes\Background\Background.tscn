[gd_scene load_steps=4 format=2]

[ext_resource path="res://Art/Environment/background.png" type="Texture" id=1]
[ext_resource path="res://Scenes/Background/Background.gd" type="Script" id=2]
[ext_resource path="res://Scenes/Background/Background.tres" type="Animation" id=3]

[node name="Background" type="Sprite" index="0"]

scale = Vector2( 0.347369, 0.416672 )
texture = ExtResource( 1 )
centered = false
region_enabled = true
region_rect = Rect2( 468.815, 0, 1408, 1536 )
script = ExtResource( 2 )
_sections_unfolded = [ "Offset", "Region", "Transform" ]

[node name="AnimationPlayer" type="AnimationPlayer" parent="." index="0"]

root_node = NodePath("..")
autoplay = ""
playback_process_mode = 1
playback_default_blend_time = 0.0
playback_speed = 1.0
anims/scrolling = ExtResource( 3 )
blend_times = [  ]


