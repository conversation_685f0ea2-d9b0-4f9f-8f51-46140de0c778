[gd_resource type="Animation" format=2]

[resource]

resource_name = "flying"
length = 1.6
loop = false
step = 0.1
tracks/0/type = "value"
tracks/0/path = NodePath("AnimatedSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.4, 0.6, 0.8, 1, 1.2, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ 0, 1, 2, 3, 4, 5, 6, 7 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("CollisionPolygon2D:polygon")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.4, 0.6, 0.8, 1, 1.2, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ PoolVector2Array( 282.635, 268.92, 286.656, 276.787, 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 335.956, 287.451, 334.033, 284.654, 339.278, 285.703, 343.299, 289.899, 344.872, 294.269, 347.67, 291.472, 348.893, 287.801, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.614, 325.563, 362.005, 325.038, 368.823, 327.835, 370.572, 330.808, 369.698, 333.78, 365.851, 335.703, 360.257, 336.752, 367.774, 339.724, 368.823, 342.521, 368.474, 344.619, 365.152, 347.241, 357.285, 348.465, 347.67, 346.542, 341.726, 343.22, 326.166, 346.891, 310.607, 347.766, 307.984, 352.486, 302.915, 356.682, 294.873, 359.304, 294.173, 353.884, 301.516, 350.912, 304.488, 347.766, 293.649, 347.241, 290.852, 351.612, 284.908, 355.283, 274.593, 356.856, 275.292, 350.738, 282.46, 350.563, 287.53, 346.717, 275.992, 343.395, 268.299, 337.626, 264.278, 329.059, 263.754, 323.465, 259.558, 318.919, 257.985, 310.528, 258.509, 306.332, 254.488, 301.262, 254.313, 295.493, 258.334, 290.423, 256.236, 283.08, 258.334, 274.514, 262.006, 270.143, 266.376, 278.01, 277.915, 289.899, 278.439, 289.549, 277.04, 281.857, 278.439, 274.514 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 335.956, 287.451, 334.033, 284.654, 339.278, 285.703, 343.299, 289.899, 344.872, 294.269, 347.67, 291.472, 348.893, 287.801, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.614, 325.563, 362.005, 325.038, 368.823, 327.835, 370.572, 330.808, 369.698, 333.78, 365.851, 335.703, 360.257, 336.752, 367.774, 339.724, 368.823, 342.521, 368.474, 344.619, 365.152, 347.241, 357.285, 348.465, 347.67, 346.542, 341.726, 343.22, 326.166, 346.891, 310.607, 347.766, 307.984, 352.486, 302.915, 356.682, 294.873, 359.304, 294.173, 353.884, 301.516, 350.912, 304.488, 347.766, 293.649, 347.241, 290.852, 351.612, 284.908, 355.283, 274.593, 356.856, 275.292, 350.738, 282.46, 350.563, 287.53, 346.717, 275.992, 343.395, 268.299, 337.626, 264.278, 329.059, 264.278, 326.612, 257.111, 323.465, 254.138, 317.171, 250.642, 310.353, 252.082, 307.646, 248.544, 302.311, 248.894, 296.542, 256.411, 301.087, 265.327, 303.535, 264.978, 296.192, 271.621, 300.563, 281.761, 290.073 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 335.956, 287.451, 334.033, 284.654, 339.278, 285.703, 343.299, 289.899, 344.872, 294.269, 347.67, 291.472, 348.893, 287.801, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.614, 325.563, 362.005, 325.038, 368.823, 327.835, 370.572, 330.808, 369.698, 333.78, 365.851, 335.703, 360.257, 336.752, 367.774, 339.724, 368.823, 342.521, 368.474, 344.619, 365.152, 347.241, 357.285, 348.465, 347.67, 346.542, 341.726, 343.22, 323.194, 347.328, 304.488, 380.37, 303.089, 389.81, 294.348, 382.293, 291.726, 372.328, 287.18, 368.307, 280.712, 379.845, 279.138, 389.461, 270.922, 383.167, 267.6, 372.328, 261.656, 365.859, 261.656, 359.216, 260.083, 349.251, 263.404, 343.919, 270.572, 339.024, 268.299, 337.626, 264.453, 330.283, 263.579, 319.968, 267.076, 308.605, 271.621, 300.563, 281.761, 290.073 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 335.956, 287.451, 334.033, 284.654, 339.278, 285.703, 343.299, 289.899, 344.872, 294.269, 347.67, 291.472, 348.893, 287.801, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.614, 325.563, 362.005, 325.038, 368.823, 327.835, 370.572, 330.808, 369.698, 333.78, 365.851, 335.703, 360.257, 336.752, 367.774, 339.724, 368.823, 342.521, 368.474, 344.619, 365.152, 347.241, 357.285, 348.465, 347.67, 346.542, 341.726, 343.22, 328.031, 347.033, 310.791, 348.195, 308.273, 353.038, 303.283, 356.241, 295.294, 359.817, 290.064, 366.791, 289.095, 373.765, 283.865, 369.89, 281.928, 364.079, 277.666, 357.106, 274.567, 355.943, 266.431, 366.21, 265.075, 373.765, 259.457, 369.116, 257.52, 363.692, 253.84, 357.687, 255.196, 351.682, 254.421, 345.289, 257.908, 341.996, 267.981, 336.96, 264.453, 330.283, 263.579, 319.968, 267.076, 308.605, 271.621, 300.563, 281.761, 290.073 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 332.68, 285.726, 337.717, 286.695, 340.816, 288.632, 343.299, 289.899, 344.872, 294.269, 346.24, 290.182, 347.596, 286.695, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.214, 320.207, 362.706, 317.108, 369.873, 318.657, 372.198, 322.919, 366.967, 329.699, 354.182, 336.672, 365.224, 342.484, 368.323, 347.52, 366.967, 351.782, 358.25, 353.525, 347.596, 348.683, 341.726, 343.22, 328.031, 347.033, 310.791, 348.195, 308.273, 353.038, 303.283, 356.241, 295.294, 359.817, 294.713, 354.106, 300.718, 352.169, 304.592, 347.908, 293.551, 347.908, 289.87, 352.75, 284.64, 355.269, 277.666, 357.106, 274.567, 355.943, 275.729, 351.007, 282.03, 350.766, 287.546, 346.939, 275.342, 342.871, 267.981, 336.96, 264.453, 330.283, 263.138, 323.694, 258.683, 316.333, 258.101, 306.066, 254.615, 301.03, 254.615, 294.831, 257.908, 290.375, 256.552, 285.92, 256.939, 277.59, 262.363, 270.229, 267.012, 279.334, 278.441, 289.794, 277.085, 283.402, 279.022, 273.329, 282.703, 269.648, 285.415, 275.653 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 332.68, 285.726, 337.717, 286.695, 340.816, 288.632, 343.299, 289.899, 344.872, 294.269, 346.24, 290.182, 347.596, 286.695, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.214, 320.207, 362.706, 317.108, 369.873, 318.657, 372.198, 322.919, 366.967, 329.699, 354.182, 336.672, 365.224, 342.484, 368.323, 347.52, 366.967, 351.782, 358.25, 353.525, 347.596, 348.683, 341.726, 343.22, 328.031, 347.033, 310.791, 348.195, 308.273, 353.038, 303.283, 356.241, 295.294, 359.817, 294.713, 354.106, 300.718, 352.169, 304.592, 347.908, 293.551, 347.908, 289.87, 352.75, 284.64, 355.269, 277.666, 357.106, 274.567, 355.943, 275.729, 351.007, 282.03, 350.766, 287.546, 346.939, 275.342, 342.871, 267.981, 336.96, 264.453, 330.283, 263.525, 326.212, 257.133, 323.306, 253.84, 316.526, 251.322, 312.458, 251.871, 307.158, 249.191, 303.548, 248.997, 295.993, 256.745, 301.417, 267.593, 304.323, 266.818, 295.799, 272.63, 299.674, 284.446, 288.245 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 332.68, 285.726, 337.717, 286.695, 340.816, 288.632, 343.299, 289.899, 344.872, 294.269, 346.24, 290.182, 347.596, 286.695, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.214, 320.207, 362.706, 317.108, 369.873, 318.657, 372.198, 322.919, 366.967, 329.699, 354.182, 336.672, 365.224, 342.484, 368.323, 347.52, 366.967, 351.782, 358.25, 353.525, 347.596, 348.683, 341.726, 343.22, 328.031, 347.033, 325.126, 352.309, 308.854, 379.235, 307.304, 389.308, 304.398, 388.727, 298.393, 381.753, 295.682, 372.455, 289.483, 364.901, 281.347, 378.654, 278.829, 389.308, 269.724, 381.366, 267.593, 372.509, 262.751, 367.473, 260.813, 359.143, 259.845, 350.62, 263.525, 343.84, 269.918, 339.772, 267.981, 336.96, 264.453, 330.283, 263.525, 326.212, 265.462, 311.684, 272.63, 299.674, 284.446, 288.245 ), PoolVector2Array( 292.95, 283.605, 305.362, 280.458, 317.949, 281.507, 327.39, 285.178, 336.481, 290.948, 332.68, 285.726, 337.717, 286.695, 340.816, 288.632, 343.299, 289.899, 344.872, 294.269, 346.24, 290.182, 347.596, 286.695, 351.516, 292.521, 351.166, 298.115, 349.068, 302.311, 347.145, 303.185, 353.089, 315.073, 353.214, 320.207, 362.706, 317.108, 369.873, 318.657, 372.198, 322.919, 366.967, 329.699, 354.182, 336.672, 365.224, 342.484, 368.323, 347.52, 366.967, 351.782, 358.25, 353.525, 347.596, 348.683, 341.726, 343.22, 329.881, 346.997, 311.091, 348.353, 308.573, 353.003, 302.18, 357.458, 295.013, 359.395, 293.851, 354.746, 284.359, 366.175, 283.003, 373.923, 277.967, 370.243, 275.255, 363.463, 271.768, 358.62, 266.538, 365.594, 264.688, 374.094, 259.845, 369.832, 257.908, 363.827, 253.646, 357.435, 255.002, 351.042, 254.615, 345.231, 259.845, 340.194, 267.981, 336.96, 264.453, 330.283, 263.525, 326.212, 265.462, 311.684, 272.63, 299.674, 284.446, 288.245 ) ]
}

