@echo off
echo ========================================
echo    Flappy 9/11 Web Game Launcher
echo ========================================
echo.
echo Starting local web server...
echo.
echo Choose your preferred method:
echo 1. Python (recommended)
echo 2. Node.js http-server
echo 3. PHP built-in server
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Starting Python HTTP server...
    echo Open your browser and go to: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    python -m http.server 8000
) else if "%choice%"=="2" (
    echo.
    echo Starting Node.js HTTP server...
    echo Open your browser and go to: http://localhost:8080
    echo Press Ctrl+C to stop the server
    echo.
    npx http-server -p 8080
) else if "%choice%"=="3" (
    echo.
    echo Starting PHP HTTP server...
    echo Open your browser and go to: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    php -S localhost:8000
) else (
    echo.
    echo Invalid choice. Please run the script again.
    pause
    exit
)

echo.
echo Server stopped.
pause
