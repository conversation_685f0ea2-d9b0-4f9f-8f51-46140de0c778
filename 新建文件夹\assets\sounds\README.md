# 音频文件说明

这个文件夹应该包含以下音频文件：

## 必需的音频文件

1. **sfx_wing.ogg** / **sfx_wing.mp3** - 飞行音效
   - 当玩家按空格键或点击时播放
   - 短促的"扑翅"声音

2. **sfx_point.ogg** / **sfx_point.mp3** - 得分音效
   - 当玩家通过管道时播放
   - 清脆的得分提示音

3. **sfx_hit.ogg** / **sfx_hit.mp3** - 撞击音效
   - 当玩家撞到管道时播放
   - 撞击声

4. **sfx_die.ogg** / **sfx_die.mp3** - 死亡音效
   - 当玩家死亡时播放
   - 失败音效

5. **sfx_swooshing.ogg** / **sfx_swooshing.mp3** - 界面切换音效
   - 界面切换时播放
   - 轻柔的"嗖"声

## 音频格式

- 推荐使用 OGG 格式（更好的压缩率）
- 提供 MP3 作为备用格式（更好的兼容性）
- 文件大小应该尽量小（建议每个文件小于 50KB）

## 获取音频文件

你可以：

1. **从原始Godot项目复制**：
   - 查看 `Audio/Sounds/` 文件夹
   - 将 `.wav` 文件转换为 `.ogg` 和 `.mp3`

2. **使用免费音效库**：
   - Freesound.org
   - Zapsplat.com
   - Adobe Audition 内置音效

3. **自己录制**：
   - 使用 Audacity 等免费软件
   - 录制简短的音效

## 转换工具

- **FFmpeg**：命令行音频转换工具
- **Audacity**：免费音频编辑软件
- **在线转换器**：如 CloudConvert

## 示例 FFmpeg 命令

```bash
# 将 WAV 转换为 OGG
ffmpeg -i input.wav -c:a libvorbis -q:a 4 output.ogg

# 将 WAV 转换为 MP3
ffmpeg -i input.wav -c:a libmp3lame -b:a 128k output.mp3
```

## 注意事项

- 如果没有音频文件，游戏仍然可以运行（已添加错误处理）
- 音频文件缺失时会在控制台显示警告
- 建议添加音频文件以获得完整的游戏体验
