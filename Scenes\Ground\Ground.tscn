[gd_scene load_steps=5 format=2]

[ext_resource path="res://Scenes/Ground/Ground.gd" type="Script" id=1]
[ext_resource path="res://Art/Environment/ground.png" type="Texture" id=2]
[ext_resource path="res://Scenes/Ground/Ground.tres" type="Animation" id=3]

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 300.137, 35.5443 )

[node name="Ground" type="Area2D"]
position = Vector2( 238.911, 451.342 )
collision_layer = 16
collision_mask = 0
script = ExtResource( 1 )

[node name="Sprite" type="Sprite" parent="."]
position = Vector2( 3.30785, 152.048 )
scale = Vector2( 0.292619, 0.374796 )
texture = ExtResource( 2 )
region_enabled = true
region_rect = Rect2( 0, 1328, 2050, 194 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/scrolling = ExtResource( 3 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( 1, 151.465 )
shape = SubResource( 1 )
