@echo off
echo Starting local web server for Flappy 9/11...
echo.
echo Choose your preferred method:
echo 1. Python (recommended)
echo 2. Node.js (if you have it installed)
echo 3. PHP (if you have it installed)
echo.
set /p choice="Enter your choice (1-3): "

cd web

if "%choice%"=="1" (
    echo Starting Python HTTP server...
    echo Open your browser and go to: http://localhost:8000
    echo Press Ctrl+C to stop the server
    python -m http.server 8000
) else if "%choice%"=="2" (
    echo Starting Node.js HTTP server...
    echo Open your browser and go to: http://localhost:8080
    echo Press Ctrl+C to stop the server
    npx http-server -p 8080
) else if "%choice%"=="3" (
    echo Starting PHP HTTP server...
    echo Open your browser and go to: http://localhost:8000
    echo Press Ctrl+C to stop the server
    php -S localhost:8000
) else (
    echo Invalid choice. Please run the script again.
    pause
)

pause
