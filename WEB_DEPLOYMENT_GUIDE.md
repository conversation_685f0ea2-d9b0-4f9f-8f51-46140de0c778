# Flappy 9/11 Web版本部署指南

## 概述

这个项目已经成功配置为支持Web版本导出。现在你可以将Flappy 9/11游戏导出为HTML5/Web版本，在浏览器中运行。

## 已完成的配置

### 1. 导出预设配置
- ✅ 在 `export_presets.cfg` 中添加了HTML5导出预设
- ✅ 配置了Web优化的导出选项
- ✅ 设置导出路径为 `web/index.html`

### 2. 项目优化
- ✅ 在 `project.godot` 中添加了Web友好的设置
- ✅ 启用了GLES2渲染器（Web兼容性更好）
- ✅ 配置了适当的纹理压缩设置
- ✅ 添加了HTML5特定配置

### 3. Web部署文件
- ✅ 创建了 `web/index.html` - 游戏容器页面
- ✅ 创建了 `web/README.md` - Web版本说明
- ✅ 创建了 `web/.htaccess` - Apache服务器配置
- ✅ 创建了 `start_local_server.bat` - 本地测试脚本

## 如何导出Web版本

### 步骤1：安装Godot
1. 下载 [Godot 3.x](https://godotengine.org/download) （重要：不是4.x版本）
2. 安装HTML5导出模板：
   - 打开Godot编辑器
   - 进入 `Editor` → `Manage Export Templates`
   - 下载并安装3.x版本的导出模板

### 步骤2：导出游戏
1. 在Godot编辑器中打开项目（双击 `project.godot`）
2. 进入 `Project` → `Export`
3. 选择 "HTML5" 导出预设（已预配置）
4. 点击 "Export Project" 按钮
5. 确认导出路径为 `web/index.html`
6. 等待导出完成

### 步骤3：测试游戏
1. 双击 `start_local_server.bat` 启动本地服务器
2. 选择Python选项（推荐）
3. 在浏览器中访问 `http://localhost:8000`
4. 游戏应该正常加载和运行

## 部署到Web服务器

### 免费托管选项

#### GitHub Pages
1. 创建GitHub仓库
2. 将 `web` 文件夹内容推送到 `gh-pages` 分支
3. 在仓库设置中启用GitHub Pages
4. 访问 `https://yourusername.github.io/yourrepo`

#### Netlify
1. 访问 [netlify.com](https://netlify.com)
2. 将 `web` 文件夹拖拽到部署区域
3. 获得自动生成的URL

#### Vercel
1. 访问 [vercel.com](https://vercel.com)
2. 导入项目或上传 `web` 文件夹
3. 获得自动生成的URL

### 自有服务器
1. 将 `web` 文件夹内容上传到服务器
2. 确保服务器支持HTTPS
3. 配置正确的MIME类型（`.htaccess` 文件已包含）

## 游戏特性

### 控制方式
- 🖱️ **鼠标左键**：让飞机飞起来
- ⌨️ **空格键**：让飞机飞起来
- 📱 **触摸**：在移动设备上点击屏幕

### 响应式设计
- 自动适配不同屏幕尺寸
- 支持全屏模式
- 移动设备友好

### 浏览器兼容性
- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## 故障排除

### 常见问题

**Q: 游戏无法加载，显示空白页面**
A: 确保通过HTTP服务器访问，不要直接双击HTML文件

**Q: 游戏加载很慢**
A: Web版本文件较大，首次加载需要时间。考虑启用GZIP压缩

**Q: 在移动设备上无法控制**
A: 确保触摸事件正常工作，检查浏览器控制台是否有错误

**Q: 音频无法播放**
A: 现代浏览器要求用户交互后才能播放音频，点击游戏区域即可

### 调试技巧
1. 打开浏览器开发者工具（F12）
2. 查看控制台是否有错误信息
3. 检查网络标签页确认所有文件已加载
4. 清除浏览器缓存后重试

## 文件结构

```
FlappyNineEleven/
├── project.godot          # 项目配置（已优化Web设置）
├── export_presets.cfg     # 导出预设（包含HTML5配置）
├── start_local_server.bat # 本地测试脚本
├── web/                   # Web部署文件夹
│   ├── index.html         # 游戏页面
│   ├── README.md          # Web版本说明
│   ├── .htaccess          # 服务器配置
│   └── [导出后的游戏文件]
└── [其他游戏资源文件]
```

## 下一步

1. 使用Godot导出HTML5版本
2. 测试本地运行
3. 部署到你选择的Web平台
4. 分享游戏链接给朋友！

## 技术细节

- **渲染器**: GLES2（Web兼容性最佳）
- **分辨率**: 480x800（竖屏）
- **导出格式**: HTML5 + WebAssembly
- **压缩**: 启用纹理压缩优化文件大小
