<!DOCTYPE html>
<html lang="en">
   <head>
      <title>Flappy 9/11 - Web Game</title>
      <meta http-equiv="content-type" content="text/html; charset=utf-8" />
      <meta name="author" content="Game Developer" />
      <meta name="description" content="Play Flappy 9/11 - A challenging flying game!" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />

      <!-- Open Graph tags -->
      <meta property="og:title" content="Flappy 9/11 - Web Game" />
      <meta property="og:description" content="Play Flappy 9/11 - A challenging flying game!" />
      <meta property="og:type" content="website" />
      <meta property="og:image" content="assets/plane.png" />
      <meta property="og:site_name" content="Flappy 9/11 Game" />
      <!-- Style sheets -->
      <link href="css/reset.css" rel="stylesheet">
      <link href="css/main.css" rel="stylesheet">
      <script type="text/javascript">var switchTo5x=true;</script>
<script type="text/javascript">stLight.options({publisher: "2ae97540-b48e-49c1-9d0f-e9e34df112fe", doNotHash: true, doNotCopy: true, hashAddressBar: false});</script>
 </head>
   <body>

      <div id="gamecontainer">
	<style type="text/css">
	#legpad {display: none}
	#smallpad {display: none}
	#ip4ad {display: none}
	#hdad { display: none }
	#leftad {display: none; float: left; padding-left: 50px; padding-top:75px}
	#rightad {display: none; float: right; padding-right: 50px; padding-top:75px}
	.largetab { display: none }
	@media (orientation: portrait) and (min-width: 319px) and (max-width: 459px) {#ip4ad {display: block} }
	@media (orientation: portrait) and (min-width: 299px) and (max-width: 318px) {#smallpad {display: block} }
	@media (orientation: portrait) and (min-width: 200px) and (max-width: 298px) {#legpad {display: block} }
	@media (orientation: landscape) and (min-height: 600px) and (min-width: 1270px) { #leftad {display: block;} #rightad {display: block;} #hdad { display: block; } }
	@media (orientation: portrait) and (min-width: 760px) { .largetab { display: inline-block } }
	@media (orientation: portrait) and (min-width: 460px) and (max-width: 759px) { #hdad {display: block} }
	</style>
	

         <div id="gamescreen">
            <div id="sky" class="animated">
               <div id="flyarea">
                  <div id="ceiling" class="animated"></div>
                  <!-- This is the flying and pipe area container -->
                  <div id="player" class="bird animated"></div>
                  
                  <div id="bigscore"></div>
                  
                  <div id="splash"></div>
                  
                  <div id="scoreboard">
                     <div id="medal"></div>
                     <div id="currentscore"></div>
                     <div id="highscore"></div>
                     <div id="replay"><img src="assets/replay.png" alt="replay"></div>
                  </div>
                  
                  <!-- Pipes go here! -->
               </div>
            </div>
            <div id="land" class="animated"><div id="debug"></div></div>
         </div>
      </div>
      <div id="footer">
	
      </div>

      <!-- Debug elements -->
      <div class="boundingbox" id="playerbox"></div>
      <div class="boundingbox" id="pipebox"></div>

      <!-- Loading indicator -->
      <div id="loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 18px; z-index: 9999; display: none;">
         Loading game...
      </div>

      <!-- Error message -->
      <div id="error" style="position: fixed; top: 20px; left: 20px; right: 20px; background: rgba(255,0,0,0.8); color: white; padding: 10px; border-radius: 5px; z-index: 9999; display: none;">
         <strong>Error:</strong> <span id="error-message"></span>
      </div>
      
      <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>
      <script src="js/jquery.transit.min.js"></script>
      <script src="js/buzz.min.js"></script>
      <script src="js/game.js"></script>
    
	
   </body>
</html>
