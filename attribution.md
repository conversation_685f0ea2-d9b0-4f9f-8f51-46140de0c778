# Attribution

In order to track attribution and ensure I don't forget any elements, all the attribution will be collected here.  All items which are unattributed are assumed to be my own creations.  If you notice a work with a missing or false attribution, please contact me and I will update the entry as soon as possible.

## Art

[Flying](Art/Flying) and [hit](Art/Hit) animation frames created by bevouliin.com of Open Game Art.  The submission may be found [here](https://opengameart.org/content/bevouliin-green-flappy-bird-sprite-sheets) and their profile may be found [here](https://opengameart.org/users/bevouliincom).  The artist's website for further game assets may be found [here](https://bevouliin.com/).

[background.png](Art/Environment/background.png) created by bevouliin.com of Open Game Art.  The submission may be found [here](https://opengameart.org/content/bevouliin-free-game-background-for-game-developers) and their profile may be found [here](https://opengameart.org/users/bevouliincom).  The artist's website for further game assets may be found [here](https://bevouliin.com/).

[pipe.png](Art/Environment/pipe.png) and [pipe-top](Art/Environment/pipe-top.png) created by <PERSON> and <PERSON> of the PlatForge project, and posted by thomaswp of Open Game Art.  The submission may be found [here](https://opengameart.org/content/2d-object-pack) and their profile may be found [here](https://opengameart.org/users/thomaswp).  Further art from PlatForge may be found [here](http://opengameart.org/content/art-from-platforge).  
[full-pipe.png](Art/Environment/full-pipe.png) is a combination of the pipe.png and pipe-top.png pieces.

## Audio

[bgm.wav](Audio/Music/bgm.wav) created by syncopika of Open Game Art.  The submission may be found [here](https://opengameart.org/content/calm-bgm) and their profile may be found [here](https://opengameart.org/users/syncopika).

[flap.wav](Audio/Sounds/flap.wav) and [splat.wav](Audio/Sounds/splat.wav) created by Blender Foundation and uploaded by Lamoot of Open Game Art.  The submission may be found [here](https://opengameart.org/content/flap-splat-poo-yo-frankie), the original creator's website may be found [here](http://apricot.blender.org/), and their profile may be found [here](https://opengameart.org/users/lamoot).

## Fonts

[Arcadepix Plus.ttf](Fonts/Arcadepix%20Plus.ttf) created by Jimmy Campbell of DaFont.  The submission may be found [here](https://www.dafont.com/arcadepix-plus.font) and their profile may be found [here](https://www.dafont.com/jimmy-campbell.d5241).
