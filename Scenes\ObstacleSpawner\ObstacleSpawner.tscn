[gd_scene load_steps=3 format=2]

[ext_resource path="res://Scenes/ObstacleSpawner/ObstacleSpawner.gd" type="Script" id=1]

[sub_resource type="RectangleShape2D" id=1]

custom_solver_bias = 0.0
extents = Vector2( 321, 10 )

[node name="ObstacleSpawner" type="Node2D"]

position = Vector2( 680, 0 )
script = ExtResource( 1 )
_sections_unfolded = [ "Transform", "Visibility" ]

[node name="DestructionZone" type="Area2D" parent="." index="0"]

position = Vector2( -880, 320 )
input_pickable = true
gravity_vec = Vector2( 0, 1 )
gravity = 98.0
linear_damp = 0.1
angular_damp = 1.0
collision_layer = 8
collision_mask = 0
audio_bus_override = false
audio_bus_name = "Master"
_sections_unfolded = [ "Collision", "Transform" ]

[node name="CollisionShape2D" type="CollisionShape2D" parent="DestructionZone" index="0"]

rotation = -1.5708
shape = SubResource( 1 )
one_way_collision = true
_sections_unfolded = [ "Transform" ]

[node name="GenerationTimeout" type="Timer" parent="." index="1"]

process_mode = 1
wait_time = 2.0
one_shot = false
autostart = false

[node name="GeneratedObstacles" type="Node2D" parent="." index="2"]

_sections_unfolded = [ "Transform" ]

[connection signal="timeout" from="GenerationTimeout" to="." method="_on_GenerationTimeout_timeout"]


