[gd_scene load_steps=8 format=2]

[ext_resource path="res://Scenes/Main/Main.gd" type="Script" id=1]
[ext_resource path="res://Scenes/Background/Background.tscn" type="PackedScene" id=2]
[ext_resource path="res://Scenes/ObstacleSpawner/ObstacleSpawner.tscn" type="PackedScene" id=3]
[ext_resource path="res://Scenes/HUD/HUD.tscn" type="PackedScene" id=4]
[ext_resource path="res://Scenes/Bird/Bird.tscn" type="PackedScene" id=5]
[ext_resource path="res://Scenes/Ground/Ground.tscn" type="PackedScene" id=6]
[ext_resource path="res://Audio/Music/bgm.wav" type="AudioStream" id=7]

[node name="Main" type="Node"]
script = ExtResource( 1 )

[node name="Background" parent="." instance=ExtResource( 2 )]
position = Vector2( -37.0124, -105.193 )
scale = Vector2( 0.399278, 0.568737 )

[node name="ObstacleSpawner" parent="." instance=ExtResource( 3 )]

[node name="StartObstacleSpawningTimeout" type="Timer" parent="."]
wait_time = 0.25
one_shot = true

[node name="HUD" parent="." instance=ExtResource( 4 )]

[node name="Bird" parent="." instance=ExtResource( 5 )]

[node name="Ground" parent="." instance=ExtResource( 6 )]
position = Vector2( 242.133, 615.043 )

[node name="BackgroundMusic" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 7 )
[connection signal="score_point" from="ObstacleSpawner" to="." method="_on_ObstacleSpawner_score_point"]
[connection signal="timeout" from="StartObstacleSpawningTimeout" to="." method="_on_StartObstacleSpawningTimeout_timeout"]
[connection signal="restart" from="HUD" to="." method="_on_HUD_restart"]
[connection signal="death" from="Bird" to="." method="_on_Bird_death"]
[connection signal="start_flight" from="Bird" to="." method="_on_Bird_start_flight"]
