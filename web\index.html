<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Flappy 9/11 - Web Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #222;
            color: white;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        #gameContainer {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 100vw;
            max-height: 100vh;
        }
        
        #canvas {
            display: block;
            margin: 0 auto;
            border: 2px solid #444;
            background-color: #000;
        }
        
        #loadingText {
            margin-top: 20px;
            font-size: 18px;
            text-align: center;
        }
        
        #controls {
            margin-top: 20px;
            text-align: center;
            max-width: 480px;
        }
        
        .control-info {
            margin: 10px 0;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
        }
        
        .fullscreen-btn {
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .fullscreen-btn:hover {
            background-color: #45a049;
        }
        
        @media (max-width: 600px) {
            #canvas {
                border: none;
            }
            
            #controls {
                font-size: 14px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="canvas" width="480" height="800">
            Your browser does not support the HTML5 canvas tag.
        </canvas>
        
        <div id="loadingText">Loading game...</div>
        
        <div id="controls">
            <div class="control-info">
                <strong>Controls:</strong><br>
                🖱️ Click or 🚀 Spacebar to fly
            </div>
            <div class="control-info">
                Navigate through the obstacles and try to get the highest score!
            </div>
            <button class="fullscreen-btn" onclick="toggleFullscreen()">Toggle Fullscreen</button>
        </div>
    </div>

    <script>
        // Fullscreen functionality
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }
        
        // Game loading script will be inserted here by Godot export
        // This is a placeholder for the actual Godot web export files
        
        // Hide loading text when game starts
        function hideLoading() {
            const loadingText = document.getElementById('loadingText');
            if (loadingText) {
                loadingText.style.display = 'none';
            }
        }
        
        // Show error message if game fails to load
        function showError(message) {
            const loadingText = document.getElementById('loadingText');
            if (loadingText) {
                loadingText.innerHTML = `<span style="color: red;">Error: ${message}</span>`;
            }
        }
        
        // Responsive canvas sizing
        function resizeCanvas() {
            const canvas = document.getElementById('canvas');
            const container = document.getElementById('gameContainer');
            
            if (canvas && container) {
                const maxWidth = Math.min(window.innerWidth - 20, 480);
                const maxHeight = Math.min(window.innerHeight - 200, 800);
                
                // Maintain aspect ratio
                const aspectRatio = 480 / 800;
                let newWidth = maxWidth;
                let newHeight = newWidth / aspectRatio;
                
                if (newHeight > maxHeight) {
                    newHeight = maxHeight;
                    newWidth = newHeight * aspectRatio;
                }
                
                canvas.style.width = newWidth + 'px';
                canvas.style.height = newHeight + 'px';
            }
        }
        
        // Initialize
        window.addEventListener('load', function() {
            resizeCanvas();
        });
        
        window.addEventListener('resize', resizeCanvas);
        
        // Prevent context menu on right click
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Prevent zoom on mobile
        document.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });
        
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(e) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
