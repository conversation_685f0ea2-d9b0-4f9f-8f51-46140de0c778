# Flappy 9/11 - Web Version

这是Flappy 9/11游戏的Web版本部署文件夹。

## 如何导出Web版本

1. **安装Godot 3.x**
   - 下载并安装Godot 3.x版本（不是4.x）
   - 确保安装了HTML5导出模板

2. **导出游戏**
   - 在Godot编辑器中打开项目
   - 进入 `Project` → `Export`
   - 选择 "HTML5" 导出预设
   - 设置导出路径为 `web/index.html`
   - 点击 "Export Project"

3. **部署到Web服务器**
   - 将整个 `web` 文件夹上传到你的Web服务器
   - 确保服务器支持HTTPS（现代浏览器要求）
   - 访问 `index.html` 文件

## 本地测试

由于浏览器的安全限制，你不能直接双击HTML文件来运行游戏。需要通过HTTP服务器：

### 使用Python（推荐）
```bash
cd web
python -m http.server 8000
```
然后访问 `http://localhost:8000`

### 使用Node.js
```bash
cd web
npx http-server
```

### 使用PHP
```bash
cd web
php -S localhost:8000
```

## 游戏控制

- **鼠标左键**：让飞机飞起来
- **空格键**：让飞机飞起来
- **全屏按钮**：切换全屏模式

## 文件说明

- `index.html` - 主HTML文件，包含游戏容器和控制界面
- `*.js` - Godot导出的JavaScript文件（导出后生成）
- `*.wasm` - WebAssembly文件（导出后生成）
- `*.pck` - 游戏资源包（导出后生成）

## 注意事项

1. **HTTPS要求**：现代浏览器要求通过HTTPS访问WebAssembly内容
2. **文件大小**：Web版本可能比原生版本大一些
3. **性能**：Web版本性能可能略低于原生版本
4. **兼容性**：需要支持WebAssembly的现代浏览器

## 浏览器兼容性

- Chrome 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## 故障排除

如果游戏无法加载：

1. 检查浏览器控制台是否有错误信息
2. 确保通过HTTP/HTTPS服务器访问，而不是直接打开文件
3. 检查所有导出文件是否完整
4. 尝试清除浏览器缓存

## 部署示例

### GitHub Pages
1. 将 `web` 文件夹内容推送到GitHub仓库的 `gh-pages` 分支
2. 在仓库设置中启用GitHub Pages
3. 访问 `https://yourusername.github.io/yourrepo`

### Netlify
1. 将 `web` 文件夹拖拽到Netlify部署页面
2. 获得自动生成的URL

### Vercel
1. 使用Vercel CLI或网页界面部署 `web` 文件夹
2. 获得自动生成的URL
