[gd_scene load_steps=12 format=2]

[ext_resource path="res://Scenes/HUD/HUD.gd" type="Script" id=1]
[ext_resource path="res://Fonts/Arcadepix Plus.ttf" type="DynamicFontData" id=2]
[ext_resource path="res://Art/UI/restart_button.png" type="Texture" id=3]
[ext_resource path="res://Art/UI/restart_button_pressed.png" type="Texture" id=4]
[ext_resource path="res://Art/UI/restart_button_hover.png" type="Texture" id=5]

[sub_resource type="DynamicFont" id=1]
size = 100
font_data = ExtResource( 2 )

[sub_resource type="Animation" id=2]
resource_name = "fade_in_menu"
length = 0.25
step = 0.05
tracks/0/type = "value"
tracks/0/path = NodePath("Menu:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.25 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CenterContainer/TextureButton:mouse_default_cursor_shape")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.25 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ 0, 2 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CenterContainer/TextureButton:disabled")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.25 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}

[sub_resource type="Gradient" id=3]
colors = PoolColorArray( 0.376471, 0.376471, 0.376471, 0.627451, 0.376471, 0.376471, 0.376471, 0.627451 )

[sub_resource type="GradientTexture" id=4]
gradient = SubResource( 3 )

[sub_resource type="DynamicFont" id=5]
size = 32
font_data = ExtResource( 2 )

[sub_resource type="DynamicFont" id=6]
size = 64
font_data = ExtResource( 2 )

[node name="HUD" type="CanvasLayer"]
script = ExtResource( 1 )

[node name="ScoreLabel" type="Label" parent="."]
anchor_left = 0.5
anchor_right = 0.5
margin_left = -240.0
margin_right = 240.0
margin_bottom = 76.0
custom_fonts/font = SubResource( 1 )
text = "0"
align = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/fade_in_menu = SubResource( 2 )

[node name="Menu" type="Control" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0

[node name="CenterContainer" type="CenterContainer" parent="Menu"]
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MenuBackground" type="NinePatchRect" parent="Menu/CenterContainer"]
margin_left = 40.0
margin_top = 40.0
margin_right = 440.0
margin_bottom = 600.0
rect_min_size = Vector2( 400, 560 )
texture = SubResource( 4 )

[node name="CenterContainer" type="CenterContainer" parent="Menu/CenterContainer/MenuBackground"]
anchor_right = 1.0
anchor_bottom = 1.0

[node name="VBoxContainer" type="VBoxContainer" parent="Menu/CenterContainer/MenuBackground/CenterContainer"]
margin_left = 80.0
margin_top = 120.0
margin_right = 320.0
margin_bottom = 440.0
rect_min_size = Vector2( 240, 320 )
custom_constants/separation = 40
alignment = 1

[node name="CurrentScore" type="VBoxContainer" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer"]
margin_top = 25.0
margin_right = 240.0
margin_bottom = 101.0

[node name="Label" type="Label" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CurrentScore"]
margin_right = 240.0
margin_bottom = 24.0
custom_fonts/font = SubResource( 5 )
text = "YOUR SCORE"
align = 1
valign = 1

[node name="ScoreLabel" type="Label" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CurrentScore"]
margin_top = 28.0
margin_right = 240.0
margin_bottom = 76.0
custom_fonts/font = SubResource( 6 )
text = "0"
align = 1
valign = 1

[node name="PreviousBest" type="VBoxContainer" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer"]
margin_top = 141.0
margin_right = 240.0
margin_bottom = 217.0

[node name="Label" type="Label" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/PreviousBest"]
margin_right = 240.0
margin_bottom = 24.0
custom_fonts/font = SubResource( 5 )
text = "BUSH'S SCORE"
align = 1
valign = 1

[node name="ScoreLabel" type="Label" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/PreviousBest"]
margin_top = 28.0
margin_right = 240.0
margin_bottom = 76.0
custom_fonts/font = SubResource( 6 )
text = "911"
align = 1
valign = 1

[node name="CenterContainer" type="CenterContainer" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer"]
margin_top = 257.0
margin_right = 240.0
margin_bottom = 294.0

[node name="TextureButton" type="TextureButton" parent="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CenterContainer"]
margin_left = 53.0
margin_right = 187.0
margin_bottom = 37.0
hint_tooltip = "Play again!"
mouse_default_cursor_shape = 2
texture_normal = ExtResource( 3 )
texture_pressed = ExtResource( 4 )
texture_hover = ExtResource( 5 )
stretch_mode = 3
[connection signal="pressed" from="Menu/CenterContainer/MenuBackground/CenterContainer/VBoxContainer/CenterContainer/TextureButton" to="." method="_on_TextureButton_pressed"]
